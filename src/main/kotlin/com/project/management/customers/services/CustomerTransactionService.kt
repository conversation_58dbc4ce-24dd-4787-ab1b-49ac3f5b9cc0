package com.project.management.customers.services

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.exceptions.BusinessException
import com.project.management.customers.requests.CustomerTransactionRequestDto
import com.project.management.customers.requests.ModifyCustomerTransactionRequestDto
import com.project.management.customers.models.CustomerTransaction
import com.project.management.customers.requests.PatchCustomerTransactionRequestDto
import com.project.management.projects.requests.PatchRequestProjectIncomeAmount
import com.project.management.customers.mappers.CustomerTransactionMapper
import com.project.management.customers.repositories.CustomerTransactionRepository
import com.project.management.customers.validators.CustomerTransactionsValidator
import com.project.management.customers.validators.CustomerValidator
import com.project.management.projects.services.ProjectIncomesService
import com.project.management.money.project.ProjectMutateMoneyService
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Service
import java.math.BigDecimal

@Service
class CustomerTransactionService(
    private val customerTransactionsRepository: CustomerTransactionRepository,
    private val currentUser: CurrentUserConfig,
    private val customerTransactionMapper: CustomerTransactionMapper,
    private val customerValidator: CustomerValidator,
    private val customerTransactionsValidator: CustomerTransactionsValidator,
    private val projectMutateMoneyService: ProjectMutateMoneyService
) {

    @Autowired
    @Lazy
    private lateinit var projectIncomes: ProjectIncomesService

    fun getAll(): List<CustomerTransaction> {
        val user = currentUser.getCurrentUser()
        val customerTransactions =
            customerTransactionsRepository.findAllByOrganizationId(user.organizationId)
        return customerTransactions
    }

    fun getAllByCustomerId(customerId: Long): List<CustomerTransaction> {
        val user = currentUser.getCurrentUser()
        val customer = customerValidator.validateCustomerExistsByIdAndOrganizationId(
            customerId,
            user.organizationId
        )
        val customerTransactions =
            customerTransactionsRepository.findAllByOrganizationIdAndCustomerId(
                user.organizationId,
                customer.id!!
            )
        return customerTransactions
    }

    @Transactional
    fun create(transaction: CustomerTransactionRequestDto, customerId: Long): CustomerTransaction {
        val user = currentUser.getCurrentUser()
        val customer = customerValidator.validateCustomerExistsByIdAndOrganizationId(
            customerId,
            user.organizationId
        )
        val customerTransaction = customerTransactionMapper.toTransaction(
            customerTransactionRequestDto = transaction,
            customerId = customer.id!!,
            organizationId = user.organizationId,
            userId = user.id,
            createdByDetails = user,
            version = 1
        )
        val result = customerTransactionsRepository.save(customerTransaction)
        return result
    }

    @Transactional
    fun modifyAmountPaid(
        request: ModifyCustomerTransactionRequestDto,
        customerTransactionId: Long
    ): CustomerTransaction {
        val user = currentUser.getCurrentUser()
        val customerTransaction = customerTransactionsRepository.findByIdAndOrganizationId(
            customerTransactionId,
            user.organizationId
        ) ?: throw BusinessException.NotFoundException(message = "Customer transaction does not exist.")
        val incomePaidAmount = customerTransaction.amountPaid
        if (customerTransaction.version != request.version) {
            throw BusinessException.ConflictException(message = "Conflicting request, please try again.")
        }
        if (customerTransaction.amountPaid >= request.amountPaid.toBigDecimal()) {
            throw BusinessException.BadRequestException(message = "Amount paid cannot be decreased.")
        }
        if (customerTransaction.amount < request.amountPaid.toBigDecimal()) {
            throw BusinessException.BadRequestException(message = "Amount paid cannot be greater than amount.")
        }

        val updatedCustomerTransaction = customerTransaction.copy(
            amountPaid = request.amountPaid.toBigDecimal(),
            updatedBy = user.id
        )

        val savedCustomerTransaction =
            customerTransactionsRepository.save(updatedCustomerTransaction)

        projectMutateMoneyService.modifyIncomes(
            projectId = customerTransaction.projectId,
            amount = BigDecimal.ZERO,
            paid = incomePaidAmount.negate() + request.amountPaid.toBigDecimal(),
        )

        return savedCustomerTransaction
    }

    @Transactional
    fun updateAmount(
        request: PatchRequestProjectIncomeAmount,
        customerTransactionId: Long
    ): CustomerTransaction {
        val loggedInUser = currentUser.getCurrentUser()
        val transaction = customerTransactionsValidator.validateExistsByIdAndOrganization(
            customerTransactionId = customerTransactionId,
            organizationId = loggedInUser.organizationId
        )
        if (!loggedInUser.isAdmin) {
            throw BusinessException.ForbiddenException(message = "You do not have permission to update this transaction.")
        }

        if (transaction.version != request.transactionVersion) throw BusinessException.ConflictException()

        if (request.amountPaid > request.amount) {
            throw BusinessException.BadRequestException(
                message = "Amount paid cannot be greater than amount."
            )
        }

        val updatedTransaction = transaction.copy(
            amount = request.amount.toBigDecimal(),
            amountPaid = request.amountPaid.toBigDecimal(),
            updatedBy = loggedInUser.id!!
        )
        return customerTransactionsRepository.save(updatedTransaction)
    }

    @Transactional
    fun updateTransaction(
        request: PatchCustomerTransactionRequestDto,
        customerTransactionId: Long
    ): CustomerTransaction {
        val loggedInUser = currentUser.getCurrentUser()
        val transaction = customerTransactionsValidator.validateExistsByIdAndOrganization(
            customerTransactionId = customerTransactionId,
            organizationId = loggedInUser.organizationId
        )

        if (transaction.version != request.version) throw BusinessException.ConflictException()

        val updatedTransaction = transaction.copy(
            description = request.description ?: transaction.description,
            updatedBy = loggedInUser.id!!
        )

        return customerTransactionsRepository.save(updatedTransaction)
    }

    @Transactional
    fun delete(customerTransactionId: Long) {
        val loggedInUser = currentUser.getCurrentUser()
        val transaction = customerTransactionsValidator.validateExistsByIdAndOrganization(
            customerTransactionId = customerTransactionId,
            organizationId = loggedInUser.organizationId
        )
        customerTransactionsRepository.deleteByIdAndOrganizationId(
            id = customerTransactionId,
            organizationId = loggedInUser.organizationId,
            updatedBy = loggedInUser.id!!
        )
    }
}

