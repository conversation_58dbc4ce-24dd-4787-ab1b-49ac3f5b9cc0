package com.project.management.projects.services

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.exceptions.BusinessException
import com.project.management.projects.requests.PostRequestProjectExpense
import com.project.management.projects.models.ProjectExpense
import com.project.management.projects.requests.PatchRequestProjectExpenseAmount
import com.project.management.projects.requests.PatchRequestProjectExpense
import com.project.management.beneficiaries.services.BeneficiaryTransactionMutateService
import com.project.management.beneficiaries.validators.BeneficiaryValidator
import com.project.management.money.project.CreateProjectExpenseUseCase
import com.project.management.money.project.ProjectMutateMoneyService
import com.project.management.projects.mappers.toEntity
import com.project.management.projects.repositories.ProjectExpenseMutateRepository
import com.project.management.money.user.UserMoneyMutateService
import com.project.management.projects.validators.ProjectExpenseValidator
import com.project.management.projects.validators.ProjectValidator
import com.project.management.terms.validators.TermValidator
import com.project.management.terms.validators.TermsGroupValidator
import com.project.management.users.models.User
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class ProjectExpensesMutateService(
    private val projectExpenseRepository: ProjectExpenseMutateRepository,
    private val currentUser: CurrentUserConfig,
    private val projectValidator: ProjectValidator,
    private val termsGroupValidator: TermsGroupValidator,
    private val termValidator: TermValidator,
    private val beneficiaryValidator: BeneficiaryValidator,
    private val projectExpenseValidator: ProjectExpenseValidator,
    private val beneficiaryTransactionQueryService: BeneficiaryTransactionMutateService,
    private val projectService: ProjectMutateMoneyService,
    private val userMoneyMutateService: UserMoneyMutateService,
    private val addOutMoney: CreateProjectExpenseUseCase
) {

    @Transactional
    fun create(request: PostRequestProjectExpense, projectId: Long): ProjectExpense {
        return addOutMoney.create(request, projectId)
    }

    @Transactional
    fun updateAmount(
        request: PatchRequestProjectExpenseAmount,
        projectExpenseId: Long
    ): ProjectExpense {
        val loggedInUser = currentUser.getCurrentUser()
        val expense = projectExpenseValidator.validateExistsByIdAndOrganization(
            projectExpenseId = projectExpenseId,
            organizationId = loggedInUser.organizationId
        )
        if (!loggedInUser.isAdmin) {
            throw BusinessException.ForbiddenException(message = "You do not have permission to update this transaction.")
        }

        if (expense.beneficiaryTransaction.version != request.transactionVersion) throw BusinessException.ConflictException()

        if (request.amountPaid > request.amount) {
            throw BusinessException.BadRequestException(
                message = "Amount paid cannot be greater than amount."
            )
        }

        userMoneyMutateService.projectExpenseModify(
            userId = expense.createdBy!!,
            newAmount = request.amount.toBigDecimal(),
            expense = expense
        )

        projectService.modifyExpenses(
            projectId = expense.project.id!!,
            amount = expense.beneficiaryTransaction.amount.negate() + request.amount.toBigDecimal(),
            paid = expense.beneficiaryTransaction.amountPaid.negate() + request.amountPaid.toBigDecimal()
        )

        val updatedTransaction = beneficiaryTransactionQueryService.updateAmount(
            request = request,
            beneficiaryTransactionId = expense.beneficiaryTransaction.id!!
        )

        return projectExpenseValidator.validateExistsByIdAndOrganization(
            projectExpenseId = projectExpenseId,
            organizationId = loggedInUser.organizationId
        )
    }

    @Transactional
    fun updateProjectExpense(
        request: PatchRequestProjectExpense,
        projectExpenseId: Long
    ): ProjectExpense {
        val loggedInUser = currentUser.getCurrentUser()
        val expense = projectExpenseValidator.validateExistsByIdAndOrganization(
            projectExpenseId = projectExpenseId,
            organizationId = loggedInUser.organizationId
        ).toEntity()
        val term = request.termId?.let {
            termValidator.validateTermExistsByIdAndOrganizationId(
                termId = request.termId,
                organizationId = loggedInUser.organizationId
            )
        }
        val termsGroup = request.termsGroupId?.let {
            termsGroupValidator.validateTermsGroupExistsByIdAndOrganizationId(
                termsGroupId = request.termsGroupId,
                organizationId = loggedInUser.organizationId
            )
        }
        if (expense.version != request.version) throw BusinessException.ConflictException()
        if (term?.termsGroupId != request.termsGroupId) {
            throw BusinessException.BadRequestException(message = "Term and TermsGroup do not match.")
        }

        val updatedExpense = expense.copy(
            termsGroupId = request.termsGroupId ?: expense.termsGroupId,
            termId = request.termId ?: expense.termId,
            updatedBy = loggedInUser.id!!
        )
        val projectExpenseEntity = projectExpenseRepository.save(updatedExpense)

        return projectExpenseValidator.validateExistsByIdAndOrganization(
            projectExpenseId = projectExpenseEntity.id!!,
            organizationId = loggedInUser.organizationId
        )
    }

    @Transactional
    fun delete(projectExpenseId: Long) {
        val loggedInUser = currentUser.getCurrentUser()
        val expense = projectExpenseValidator.validateExistsByIdAndOrganization(
            projectExpenseId = projectExpenseId,
            organizationId = loggedInUser.organizationId
        )

        userMoneyMutateService.projectExpenseDelete(
            userId = expense.createdBy!!,
            amount = (expense.beneficiaryTransaction.amount).toDouble(),
            description = expense.beneficiaryTransaction.description,
            project = expense.project
        )

        beneficiaryTransactionQueryService.delete(expense.beneficiaryTransaction.id!!)

        projectExpenseRepository.deleteByIdAndOrganizationId(
            id = projectExpenseId,
            organizationId = loggedInUser.organizationId,
            updatedBy = loggedInUser.id!!
        )

        projectService.modifyExpenses(
            projectId = expense.project.id!!,
            amount = expense.beneficiaryTransaction.amount.negate(),
            paid = expense.beneficiaryTransaction.amountPaid.negate()
        )
    }

    private fun PostRequestProjectExpense.validate(user: User) {
        val termsGroup = termsGroupValidator.validateTermsGroupExistsByIdAndOrganizationId(
            termsGroupId = termsGroupId,
            organizationId = user.organizationId
        )
        val term = termValidator.validateTermExistsByIdAndOrganizationId(
            termId = termId,
            organizationId = user.organizationId
        )

        if (term.termsGroupId != termsGroup.id) {
            throw BusinessException.ConflictException(message = "Term and TermsGroup do not match.")
        }

        beneficiaryValidator.validateBeneficiaryExistsByIdAndOrganizationId(
            beneficiaryId = beneficiaryId,
            organizationId = user.organizationId
        )
    }
}