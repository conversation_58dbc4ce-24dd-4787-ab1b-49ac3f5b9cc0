package com.project.management.projects.models

import com.project.management.beneficiaries.models.BeneficiaryEntity
import com.project.management.beneficiaries.models.BeneficiaryTransaction
import com.project.management.terms.models.Term
import com.project.management.terms.models.TermsGroup
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.JoinColumn
import jakarta.persistence.OneToOne
import jakarta.persistence.PreUpdate
import jakarta.persistence.Table
import org.hibernate.annotations.SQLRestriction
import java.time.ZoneOffset
import java.time.ZonedDateTime

@SQLRestriction("deleted IS NULL")
@Entity
@Table(name = "project_expenses")
data class ProjectExpense(
    val organizationId: Long,

    @Column(name = "beneficiary_id")
    val beneficiaryId: Long,
    @Column(name = "beneficiary_transaction_id")
    val beneficiaryTransactionId: Long,
    @Column(name = "terms_group_id")
    val termsGroupId: Long,
    @Column(name = "term_id")
    val termId: Long,
    @Column(name = "project_id")
    val projectId: Long,

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "beneficiary_id", updatable = false, insertable = false)
    val beneficiary: BeneficiaryEntity,
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "beneficiary_transaction_id", updatable = false, insertable = false)
    val beneficiaryTransaction: BeneficiaryTransaction,
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "terms_group_id", updatable = false, insertable = false)
    val termsGroup: TermsGroup,
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "term_id", updatable = false, insertable = false)
    val term: Term,
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id", updatable = false, insertable = false)
    val project: ProjectEntity,

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var createdBy: Long? = null,
    var updatedBy: Long? = null,

    @Column(updatable = false)
    var createdAt: ZonedDateTime = ZonedDateTime.now(ZoneOffset.UTC),
    var updatedAt: ZonedDateTime = createdAt,
    var version: Long
) {
    @PreUpdate
    fun setLastUpdate() {
        version += 1
        updatedAt = ZonedDateTime.now(ZoneOffset.UTC)
    }
}