package com.project.management.organization.repositories

import com.project.management.organization.models.CapitalTransaction
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface CapitalTransactionRepository: JpaRepository<CapitalTransaction, Long> {

    fun findAllByOrganizationId(organizationId: Long): List<CapitalTransaction>
}