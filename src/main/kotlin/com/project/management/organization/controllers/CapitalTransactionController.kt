package com.project.management.organization.controllers

import com.project.management.money.organization.OrganizationCapitalMoneyMutateService
import com.project.management.organization.requests.CapitalTransactionRequest
import com.project.management.organization.models.CapitalTransaction
import com.project.management.organization.services.CapitalTransactionsService
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/v1/organizations/{organizationId}/capital")
class CapitalTransactionController(
    private val capitalTransactionsService: CapitalTransactionsService,
    private val capitalMoneyMutate: OrganizationCapitalMoneyMutateService
) {
    @GetMapping
    fun getAll(
        @PathVariable organizationId: Long
    ): ResponseEntity<List<CapitalTransaction>> {
        return ResponseEntity.ok(capitalTransactionsService.getAll())
    }

    @PostMapping("/add")
    fun add(
        @PathVariable organizationId: Long,
        @RequestBody capitalTransactionRequest: CapitalTransactionRequest
    ): ResponseEntity<CapitalTransaction> {
        return ResponseEntity.ok(capitalMoneyMutate.addCapital(capitalTransactionRequest))
    }

    @PostMapping("/remove")
    fun remove(
        @PathVariable organizationId: Long,
        @RequestBody capitalTransactionRequest: CapitalTransactionRequest
    ): ResponseEntity<CapitalTransaction> {
        return ResponseEntity.ok(capitalMoneyMutate.removeCapital(capitalTransactionRequest))
    }
}