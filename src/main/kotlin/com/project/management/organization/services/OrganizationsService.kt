package com.project.management.organization.services

import com.project.management.beneficiaries.services.BeneficiaryTransactionQueryService
import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.customers.services.CustomerTransactionService
import com.project.management.organization.responses.OrganizationResponseDto
import com.project.management.organization.models.OrganizationEntity
import com.project.management.organization.mappers.OrganizationMapper
import com.project.management.organization.repositories.OrganizationRepository
import com.project.management.users.services.UsersService
import com.project.management.organization.validators.OrganizationValidator
import jakarta.transaction.Transactional
import org.springframework.stereotype.Service

@Service
class OrganizationsService(
    private val organizationsRepository: OrganizationRepository,
    private val users: UsersService,
    private val expenses: BeneficiaryTransactionQueryService,
    private val incomes: CustomerTransactionService,
    private val organizationMapper: OrganizationMapper,
    private val currentUser: CurrentUserConfig,
    private val organizationValidator: OrganizationValidator
) {

    fun getById(id: Long): OrganizationResponseDto {
        val user = currentUser.getCurrentUser()
        val organization = organizationValidator.validateOrganizationExistsById(
            organizationId = id
        )
        return getOrganizationResponse(organization)
    }

    fun getUserOrganization(): OrganizationResponseDto {
        val user = currentUser.getCurrentUser()
        return getById(user.organizationId)
    }

    @Transactional
    fun increaseAvailableCapital(organizationId: Long, amount: Double): OrganizationResponseDto {
        val currentUser = currentUser.getCurrentUser()
        var organization = organizationValidator.validateOrganizationExistsById(
            organizationId = organizationId
        )
        organization = organization.copy(
            capital = organization.capital.plus(amount.toBigDecimal()),
            updatedBy = currentUser.id
        )
        return getOrganizationResponse(organizationsRepository.save(organization))
    }

    @Transactional
    fun decreaseAvailableCapital(organizationId: Long, amount: Double): OrganizationResponseDto {
        val currentUser = currentUser.getCurrentUser()
        var organization = organizationValidator.validateOrganizationExistsById(
            organizationId = organizationId
        )
        organization = organization.copy(
            capital = organization.capital.minus(amount.toBigDecimal()),
            updatedBy = currentUser.id
        )
        return getOrganizationResponse(organizationsRepository.save(organization))
    }

    @Transactional
    private fun getOrganizationResponse(organization: OrganizationEntity): OrganizationResponseDto {
        val incomes = incomes.getAll().sumOf { it.amount }
        val paidIncomes = this.incomes.getAll().sumOf { it.amountPaid }
        val expenses = expenses.getAll().sumOf { it.amountPaid }
        val balance = users.getAll().sumOf { it.balance.negate() }
        return organizationMapper.toOrganizationResponseDto(
            organization = organization,
            expenses = expenses,
            incomes = incomes,
            paidIncomes = paidIncomes,
            totalCapital = (organization.capital + paidIncomes + balance - expenses),
        )
    }
}