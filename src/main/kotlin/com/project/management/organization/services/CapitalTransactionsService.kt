package com.project.management.organization.services

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.organization.models.CapitalTransaction
import com.project.management.organization.repositories.CapitalTransactionRepository
import org.springframework.stereotype.Service

@Service
class CapitalTransactionsService(
    private val capitalTransactionsRepository: CapitalTransactionRepository,
    private val currentUser: CurrentUserConfig,
) {

    fun getAll(): List<CapitalTransaction> {
        val user = currentUser.getCurrentUser()
        val capitalTransactions =
            capitalTransactionsRepository.findAllByOrganizationId(user.organizationId)

        return capitalTransactions
    }
}