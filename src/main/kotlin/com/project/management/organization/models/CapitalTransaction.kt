package com.project.management.organization.models

import com.project.management.users.models.User
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.JoinColumn
import jakarta.persistence.OneToOne
import jakarta.persistence.PreUpdate
import jakarta.persistence.Table
import java.math.BigDecimal
import java.time.ZoneOffset
import java.time.ZonedDateTime

@Entity
@Table(name = "capital_transactions")
data class CapitalTransaction(
    val organizationId: Long,

    val amount: BigDecimal,
    val description: String,
    val transactionDate: ZonedDateTime,


    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "created_by", updatable = false, insertable = false)
    var createdByDetails: User,

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Column(name = "created_by")
    var createdBy: Long? = null,
    var updatedBy: Long? = null,

    @Column(updatable = false)
    var createdAt: ZonedDateTime = ZonedDateTime.now(ZoneOffset.UTC),
    var updatedAt: ZonedDateTime = createdAt,
) {
    @PreUpdate
    fun setLastUpdate() {
        updatedAt = ZonedDateTime.now(ZoneOffset.UTC)
    }
}