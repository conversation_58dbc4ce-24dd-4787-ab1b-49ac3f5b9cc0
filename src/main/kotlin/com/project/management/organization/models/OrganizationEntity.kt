package com.project.management.organization.models

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.PreUpdate
import jakarta.persistence.Table
import java.math.BigDecimal
import java.time.ZoneOffset
import java.time.ZonedDateTime

@Entity
@Table(name = "organizations")
data class OrganizationEntity(
    var address: String,
    var description: String,
    var email: String,
    var logoUrl: String?,
    var name: String,
    var organizationCode: String,
    var phoneNumber: String,
    var secondaryPhoneNumber: String?,
    var website: String,
    var photoUrl: String,

//    @OneToOne(fetch = FetchType.LAZY)
//    @JoinColumn(name = "owner_id")
//    var owner: User,
    val ownerId: Long,

    val capital: BigDecimal,
    val availableBalance: BigDecimal,

    val totalExpenses: BigDecimal,
    val totalPaidExpenses: BigDecimal,
    val totalIncomes: BigDecimal,
    val totalPaidIncomes: BigDecimal,

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var createdBy: Long? = null,
    var updatedBy: Long? = null,

    @Column(updatable = false)
    var createdAt: ZonedDateTime = ZonedDateTime.now(ZoneOffset.UTC),
    var updatedAt: ZonedDateTime = createdAt,
) {
    @PreUpdate
    fun setLastUpdate() {
        updatedAt = ZonedDateTime.now(ZoneOffset.UTC)
    }
}