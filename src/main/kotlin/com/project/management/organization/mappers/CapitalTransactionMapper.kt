package com.project.management.organization.mappers

import com.project.management.organization.requests.CapitalTransactionRequest
import com.project.management.organization.models.CapitalTransaction
import com.project.management.users.models.User
import java.time.ZonedDateTime

internal fun CapitalTransactionRequest.toEntity(performer: User): CapitalTransaction {
    return CapitalTransaction(
        organizationId = performer.organizationId,
        amount = amount.toBigDecimal(),
        description = description,
        transactionDate = ZonedDateTime.parse(transactionDate),
        createdBy = performer.id,
        updatedBy = performer.id,
        createdByDetails = performer
    )
}