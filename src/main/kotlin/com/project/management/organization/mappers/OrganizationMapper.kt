package com.project.management.organization.mappers

import com.project.management.organization.requests.OrganizationRequestDto
import com.project.management.organization.responses.OrganizationResponseDto
import com.project.management.organization.models.OrganizationEntity
import com.project.management.users.models.User
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.ReportingPolicy
import java.math.BigDecimal
import java.time.ZonedDateTime

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.ERROR)
interface OrganizationMapper {
    fun toOrganizationResponseDto(
        organization: OrganizationEntity,
        expenses: BigDecimal,
        incomes: BigDecimal,
        paidIncomes: BigDecimal,
        totalCapital: BigDecimal,
    ): OrganizationResponseDto
}

internal fun OrganizationRequestDto.toEntity(user: User): OrganizationEntity {
    val now = ZonedDateTime.now()
    return OrganizationEntity(
        address = "",
        description = description,
        email = "${phoneNumber}@a.c",
        logoUrl = "",
        name = name,
        organizationCode = phoneNumber,
        phoneNumber = phoneNumber,
        secondaryPhoneNumber = "",
        website = "",
        photoUrl = "",
        capital = 0.0.toBigDecimal(),
        availableBalance = 0.0.toBigDecimal(),
        totalExpenses = 0.0.toBigDecimal(),
        totalPaidExpenses = 0.0.toBigDecimal(),
        totalIncomes = 0.0.toBigDecimal(),
        totalPaidIncomes = 0.0.toBigDecimal(),
        ownerId = user.id!!,
        createdAt = now,
        updatedAt = now,
        createdBy = user.id,
        updatedBy = user.id,
    )
}