package com.project.management.organization.validators

import com.project.management.common.exceptions.BusinessException
import com.project.management.organization.models.OrganizationEntity
import com.project.management.organization.repositories.OrganizationRepository
import org.springframework.stereotype.Component
import kotlin.jvm.optionals.getOrNull

@Component
class OrganizationValidator(
    private val organizationRepository: OrganizationRepository
) {

    fun validateOrganizationExistsById(
        organizationId: Long
    ): OrganizationEntity {
        return organizationRepository.findById(organizationId).getOrNull()
            ?: throw BusinessException.NotFoundException("Organization with id $organizationId not found")
    }
}