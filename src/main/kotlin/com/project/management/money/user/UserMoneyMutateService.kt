package com.project.management.money.user

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.money.MoneyValidationService
import com.project.management.money.organization.OrganizationBalanceMutateService
import com.project.management.projects.models.ProjectEntity
import com.project.management.projects.models.ProjectExpense
import com.project.management.users.mappers.balanceTransactionEntity
import com.project.management.users.models.BalanceTransactionEntity
import com.project.management.users.models.User
import com.project.management.users.models.UserTransactionTag
import com.project.management.users.repositories.BalanceTransactionRepository
import com.project.management.users.repositories.UserRepository
import com.project.management.users.validators.UserValidator
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal

@Service
class UserMoneyMutateService(
    private val transactions: BalanceTransactionRepository,
    private val organizationBalance: OrganizationBalanceMutateService,
    private val money: MoneyValidationService,
    private val userRepository: UserRepository,
    private val currentUser: CurrentUserConfig,
    private val userValidator: UserValidator,
) {

    @Transactional
    fun projectExpenseAdd(userId: Long, expense: ProjectExpense): User {
        return decreaseBalance(
            userId = userId,
            amount = expense.beneficiaryTransaction.amountPaid.toDouble(),
            description = expense.project.description(expense.beneficiaryTransaction.description),
            tag = UserTransactionTag.PROJECT_EXPENSE_ADD,
            reference = expense.project.id.toString()
        )
    }

    @Transactional
    fun projectExpenseDelete(
        userId: Long, amount: Double, description: String, project: ProjectEntity
    ): User {
        return increaseBalance(
            userId = userId,
            amount = amount,
            description = project.description(description),
            tag = UserTransactionTag.PROJECT_EXPENSE_DELETE,
            reference = project.id.toString()
        )
    }

    @Transactional
    fun projectExpenseModify(
        userId: Long, newAmount: BigDecimal, expense: ProjectExpense
    ): User {
        val amount = (expense.beneficiaryTransaction.amount.negate() + newAmount).negate()
        val description = expense.beneficiaryTransaction.description.trim() +
                "\n" + "${expense.beneficiaryTransaction.amount}  >  $newAmount"
        return increaseBalance(
            userId = userId,
            amount = amount.toDouble(),
            description = expense.project.description(description),
            tag = UserTransactionTag.PROJECT_EXPENSE_MODIFY,
            reference = expense.project.id.toString()
        )
    }

    @Transactional
    fun userBalanceAdd(userId: Long, amount: Double): User {
        val user = increaseBalance(
            userId = userId,
            amount = amount,
            description = "",
            tag = UserTransactionTag.BALANCE_ADD,
            reference = null
        )
        organizationBalance.decreaseAvailableBalance(amount.toBigDecimal())
        money.validate(user.organizationId)
        return user
    }

    @Transactional
    fun userBalanceDecrease(userId: Long, amount: Double): User {
        val user = decreaseBalance(
            userId = userId,
            amount = amount,
            description = "",
            tag = UserTransactionTag.BALANCE_WITHDRAW,
            reference = null
        )
        organizationBalance.increaseAvailableBalance(amount.toBigDecimal())
        money.validate(user.organizationId)
        return user
    }






































    private fun increaseBalance(
        userId: Long,
        amount: Double,
        description: String,
        tag: UserTransactionTag,
        reference: String?
    ): User {
        val currentUser = currentUser.getCurrentUser()
        var user = userValidator.validateUserExistsByIdAndOrganizationId(
            userId = userId, organizationId = currentUser.organizationId
        )

        transaction(
            user = user,
            amount = amount.toBigDecimal(),
            description = description,
            tag = tag,
            reference = reference
        )

        user.balance = user.balance.plus(amount.toBigDecimal())
        user.updatedBy = currentUser.id
        user = userRepository.save(user)

        return user
    }

    private fun decreaseBalance(
        userId: Long,
        amount: Double,
        description: String,
        tag: UserTransactionTag,
        reference: String?
    ): User {
        val currentUser = currentUser.getCurrentUser()
        var user = userValidator.validateUserExistsByIdAndOrganizationId(
            userId = userId, organizationId = currentUser.organizationId
        )

        transaction(
            user = user,
            amount = amount.toBigDecimal().negate(),
            description = description,
            tag = tag,
            reference = reference
        )

        user.balance = user.balance.minus(amount.toBigDecimal())
        user.updatedBy = currentUser.id
        user = userRepository.save(user)

        return user
    }

    private fun transaction(
        user: User,
        description: String,
        amount: BigDecimal,
        tag: UserTransactionTag,
        reference: String?
    ): BalanceTransactionEntity {
        val currentAmount = user.balance + amount
        val loggedInUser = currentUser.getCurrentUser()

        val transaction = balanceTransactionEntity(
            organizationId = loggedInUser.organizationId,
            targetUserId = user.id!!,
            amount = amount,
            description = description.trim(),
            createdBy = loggedInUser,
            currentAmount = currentAmount,
            tag = tag,
            reference = reference
        )
        return transactions.save(transaction)
    }

    private fun ProjectEntity.description(description: String): String {
        return "${description.trim()}\n${this.name.trim()}".trim()
    }
}