package com.project.management.money

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.money.organization.OrganizationCapitalMoneyMutateService
import com.project.management.money.project.ProjectMoneyMutateService
import com.project.management.money.user.UserMoneyMutateService
import com.project.management.organization.models.CapitalTransaction
import com.project.management.organization.requests.CapitalTransactionRequest
import com.project.management.projects.models.ProjectExpense
import com.project.management.projects.requests.PatchRequestProjectExpenseAmount
import com.project.management.projects.requests.PostRequestProjectExpense
import com.project.management.users.models.User
import jakarta.transaction.Transactional
import org.springframework.stereotype.Component

@Component
class MoneyService(
    private val performer: CurrentUserConfig,
    private val user: UserMoneyMutateService,
    private val capital: OrganizationCapitalMoneyMutateService,
    private val project: ProjectMoneyMutateService,
    private val money: MoneyValidationService,
) {

    @Transactional
    fun userAddBalance(userId: Long, amount: Double): User {
        val response = user.userBalanceAdd(userId, amount)
        money.validate(response.organizationId)
        return response
    }

    @Transactional
    fun userDecreaseBalance(userId: Long, amount: Double): User {
        val response = user.userBalanceDecrease(userId, amount)
        money.validate(userId)
        return response
    }

    @Transactional
    fun projectExpenseAdd(request: PostRequestProjectExpense, projectId: Long): ProjectExpense {
        val response = project.projectExpenseAdd(request, projectId)
        money.validate(response.project.organizationId)
        return response
    }

    @Transactional
    fun projectExpenseUpdate(request: PatchRequestProjectExpenseAmount, projectExpenseId: Long): ProjectExpense {
        money.validate(performer.getCurrentUser().id!!)
        val response = project.projectExpenseUpdate(request, projectExpenseId)
        money.validate(response.project.organizationId)
        return response
    }

    @Transactional
    fun projectExpenseDelete(projectExpenseId: Long) {
        project.projectExpenseDelete(projectExpenseId)
        money.validate(performer.getCurrentUser().organizationId)
    }

    @Transactional
    fun addCapital(request: CapitalTransactionRequest): CapitalTransaction {
        val response = capital.addCapital(request)
        money.validate(response.organizationId)
        return response
    }

    @Transactional
    fun removeCapital(request: CapitalTransactionRequest): CapitalTransaction {
        val response = capital.removeCapital(request)
        money.validate(response.organizationId)
        return response
    }
}