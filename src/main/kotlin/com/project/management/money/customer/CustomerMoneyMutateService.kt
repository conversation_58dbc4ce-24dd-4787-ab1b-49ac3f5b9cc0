package com.project.management.money.customer

import com.project.management.customers.mappers.toCustomerTransaction
import com.project.management.customers.mappers.toEntity
import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.customers.models.Customer
import com.project.management.customers.models.CustomerTransaction
import com.project.management.customers.repositories.CustomerRepository
import com.project.management.customers.repositories.CustomerTransactionRepository
import com.project.management.customers.validators.CustomerValidator
import com.project.management.projects.requests.PostRequestProjectIncome
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal

@Service
class CustomerMoneyMutateService(
    private val transactions: CustomerTransactionRepository,
    private val customerRepository: CustomerRepository,
    private val currentUser: CurrentUserConfig,
    private val customerValidator: CustomerValidator,
) {

    @Transactional
    fun projectIncomeAdd(
        request: PostRequestProjectIncome,
        projectId: Long
    ): CustomerTransaction {
        increaseAccumulators(
            customerId = request.customerId,
            amount = request.amount.toBigDecimal(),
            amountPaid = request.amountPaid.toBigDecimal()
        )

        return createTransaction(request, projectId)
    }

    @Transactional
    fun projectIncomeDelete(
        customerId: Long,
        transactionId: Long,
        amount: BigDecimal,
        amountPaid: BigDecimal
    ): Customer {
        deleteTransaction(transactionId = transactionId)

        return decreaseAccumulators(
            customerId = customerId,
            amount = amount,
            amountPaid = amountPaid
        )
    }

    @Transactional
    fun projectIncomeModify(
        transaction: CustomerTransaction,
        newAmount: BigDecimal,
        newAmountPaid: BigDecimal
    ): Customer {
        val amount = transaction.amount.negate() + newAmount
        val amountPaid = transaction.amountPaid.negate() + newAmountPaid

        updateTransaction(transaction = transaction, amount = newAmount, amountPaid = newAmountPaid)

        return increaseAccumulators(
            customerId = transaction.customerId,
            amount = amount,
            amountPaid = amountPaid
        )
    }

    private fun increaseAccumulators(
        customerId: Long,
        amount: BigDecimal,
        amountPaid: BigDecimal
    ): Customer {
        val currentUser = currentUser.getCurrentUser()
        var customer = customerValidator.validateCustomerExistsByIdAndOrganizationId(
            customerId = customerId,
            organizationId = currentUser.organizationId
        )

        customer = customer.copy(
            balanceAccumulator = customer.balanceAccumulator.plus(amount),
            paidAccumulator = customer.paidAccumulator.plus(amountPaid),
            updatedBy = currentUser.id
        )

        return customerRepository.save(customer)
    }

    private fun decreaseAccumulators(
        customerId: Long,
        amount: BigDecimal,
        amountPaid: BigDecimal
    ): Customer {
        val currentUser = currentUser.getCurrentUser()
        var customer = customerValidator.validateCustomerExistsByIdAndOrganizationId(
            customerId = customerId,
            organizationId = currentUser.organizationId
        )

        customer = customer.copy(
            balanceAccumulator = customer.balanceAccumulator.minus(amount),
            paidAccumulator = customer.paidAccumulator.minus(amountPaid),
            updatedBy = currentUser.id
        )

        return customerRepository.save(customer)
    }

    private fun createTransaction(
        request: PostRequestProjectIncome,
        projectId: Long
    ): CustomerTransaction {
        val currentUser = currentUser.getCurrentUser()
        val entity = request
            .toCustomerTransaction(projectId)
            .toEntity(request.customerId, currentUser, 1)

        return transactions.save(entity)
    }

    private fun updateTransaction(
        transaction: CustomerTransaction,
        amount: BigDecimal,
        amountPaid: BigDecimal
    ): CustomerTransaction {
        val currentUser = currentUser.getCurrentUser()

        val updatedTransaction = transaction

        updatedTransaction.amount = amount
        updatedTransaction.amountPaid = amountPaid
        updatedTransaction.updatedBy = currentUser.id!!


        return transactions.save(updatedTransaction)
    }

    private fun deleteTransaction(transactionId: Long) {
        val performer = currentUser.getCurrentUser()
        transactions.deleteByIdAndOrganizationId(
            id = transactionId,
            organizationId = performer.organizationId,
            updatedBy = performer.id!!
        )
    }
}
