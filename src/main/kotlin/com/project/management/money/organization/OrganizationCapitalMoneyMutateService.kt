package com.project.management.money.organization

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.money.MoneyValidationService
import com.project.management.organization.mappers.toEntity
import com.project.management.organization.models.CapitalTransaction
import com.project.management.organization.models.OrganizationEntity
import com.project.management.organization.repositories.CapitalTransactionRepository
import com.project.management.organization.repositories.OrganizationRepository
import com.project.management.organization.requests.CapitalTransactionRequest
import com.project.management.organization.validators.OrganizationValidator
import com.project.management.users.models.User
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.time.ZonedDateTime

@Service
class OrganizationCapitalMoneyMutateService(
    private val capitalTransactionRepository: CapitalTransactionRepository,
    private val organizationBalanceMutateService: OrganizationBalanceMutateService,
    private val currentUser: CurrentUserConfig,
    private val organizationValidator: OrganizationValidator,
    private val organizationRepository: OrganizationRepository,
    private val money: MoneyValidationService
) {

    @Transactional
    fun addCapital(request: CapitalTransactionRequest): CapitalTransaction {
        val user = currentUser.getCurrentUser()
        val transaction = createCapitalTransaction(performer = user, request = request)

        increaseCapital(request.amount.toBigDecimal())

        money.validate(user.organizationId)
        return transaction
    }

    @Transactional
    fun removeCapital(request: CapitalTransactionRequest): CapitalTransaction {
        val user = currentUser.getCurrentUser()
        val transaction = createCapitalTransaction(
            performer = user, request = request.copy(amount = request.amount * -1)
        )

        decreaseCapital(request.amount.toBigDecimal())

        money.validate(user.organizationId)
        return transaction
    }

    @Transactional
    fun modifyCapital(
        organizationId: Long,
        amount: BigDecimal,
        description: String,
        transactionDate: ZonedDateTime = ZonedDateTime.now()
    ): CapitalTransaction {
        TODO()
        money.validate(organizationId)
    }

    private fun increaseCapital(amount: BigDecimal): OrganizationEntity {
        val performer = currentUser.getCurrentUser()
        val organization = organizationValidator.validateOrganizationExistsById(
            organizationId = performer.organizationId
        )

        val updatedOrganization = organizationBalanceMutateService.increaseAvailableBalance(amount)
            .copy(
                capital = organization.capital.add(amount),
                updatedBy = performer.id
            )

        return organizationRepository.save(updatedOrganization)
    }

    private fun decreaseCapital(amount: BigDecimal): OrganizationEntity {
        val performer = currentUser.getCurrentUser()
        val organization = organizationValidator.validateOrganizationExistsById(
            organizationId = performer.organizationId
        )

        val updatedOrganization = organizationBalanceMutateService.decreaseAvailableBalance(amount)
            .copy(
                capital = organization.capital.minus(amount),
                updatedBy = performer.id
            )

        return organizationRepository.save(updatedOrganization)
    }

    private fun modifyCapital(oldAmount: BigDecimal, newAmount: BigDecimal): OrganizationEntity {
        val performer = currentUser.getCurrentUser()
        val organization = organizationValidator.validateOrganizationExistsById(
            organizationId = performer.organizationId
        )

        val updatedOrganization = organizationBalanceMutateService
            .modifyAvailableBalance(oldAmount, newAmount)
            .copy(
                capital = organization.capital.subtract(oldAmount).add(newAmount),
                updatedBy = performer.id
            )


        return organizationRepository.save(updatedOrganization)
    }

    private fun createCapitalTransaction(
        request: CapitalTransactionRequest,
        performer: User
    ): CapitalTransaction {
        organizationValidator.validateOrganizationExistsById(
            performer.organizationId
        )

        val transaction = request.toEntity(performer = performer)

        return capitalTransactionRepository.save(transaction)
    }
}
