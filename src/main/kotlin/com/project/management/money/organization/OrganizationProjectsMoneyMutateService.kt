package com.project.management.money.organization

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.organization.models.OrganizationEntity
import com.project.management.organization.repositories.OrganizationRepository
import com.project.management.organization.validators.OrganizationValidator
import org.springframework.stereotype.Service
import java.math.BigDecimal

@Service
class OrganizationProjectsMoneyMutateService(
    private val organizations: OrganizationRepository,
    private val currentUser: CurrentUserConfig,
    private val organizationValidator: OrganizationValidator,
    private val organizationBalanceMutateService: OrganizationBalanceMutateService
) {

    fun increaseExpenses(
        amount: BigDecimal,
        amountPaid: BigDecimal
    ): OrganizationEntity {
        val currentUser = currentUser.getCurrentUser()
        var organization = organizationValidator.validateOrganizationExistsByIdAndOrganizationId(
            organizationId = currentUser.organizationId
        )
        val updated = organization.copy(
            totalExpenses = organization.totalExpenses.add(amount),
            totalPaidExpenses = organization.totalPaidExpenses.add(amountPaid),
            updatedBy = currentUser.id
        )
        organizationBalanceMutateService.decreaseAvailableBalance(amountPaid)
        return organizations.save(updated)
    }

    fun decreaseExpenses(
        amount: BigDecimal,
        amountPaid: BigDecimal
    ): OrganizationEntity {
        val currentUser = currentUser.getCurrentUser()
        val organization = organizationValidator.validateOrganizationExistsByIdAndOrganizationId(
            organizationId = currentUser.organizationId
        )
        val updated = organization.copy(
            totalExpenses = organization.totalExpenses.minus(amount),
            totalPaidExpenses = organization.totalPaidExpenses.minus(amountPaid),
            updatedBy = currentUser.id
        )
        organizationBalanceMutateService.increaseAvailableBalance(amountPaid)

        return organizations.save(updated)
    }

    fun modifyExpenses(
        oldAmount: BigDecimal,
        oldAmountPaid: BigDecimal,
        newAmount: BigDecimal,
        newAmountPaid: BigDecimal
    ): OrganizationEntity {
        val amount = oldAmount.negate() + newAmount
        val amountPaid = oldAmountPaid.negate() + newAmountPaid

        organizationBalanceMutateService.modifyAvailableBalance(oldAmountPaid, newAmountPaid)
        return increaseExpenses(amount = amount, amountPaid = amountPaid)
    }

    fun increaseIncomes(
        amount: BigDecimal,
        amountPaid: BigDecimal
    ): OrganizationEntity {
        val currentUser = currentUser.getCurrentUser()
        var organization = organizationValidator.validateOrganizationExistsByIdAndOrganizationId(
            organizationId = currentUser.organizationId
        )
        val updated = organization.copy(
            totalIncomes = organization.totalIncomes.add(amount),
            totalPaidIncomes = organization.totalPaidIncomes.add(amountPaid),
            updatedBy = currentUser.id
        )
        organizationBalanceMutateService.increaseAvailableBalance(amountPaid)

        return organizations.save(updated)
    }

    fun decreaseIncomes(
        amount: BigDecimal,
        amountPaid: BigDecimal
    ): OrganizationEntity {
        val currentUser = currentUser.getCurrentUser()
        var organization = organizationValidator.validateOrganizationExistsByIdAndOrganizationId(
            organizationId = currentUser.organizationId
        )
        val updated = organization.copy(
            totalIncomes = organization.totalIncomes.minus(amount),
            totalPaidIncomes = organization.totalPaidIncomes.minus(amountPaid),
            updatedBy = currentUser.id
        )
        organizationBalanceMutateService.decreaseAvailableBalance(amountPaid)

        return organizations.save(updated)
    }

    fun modifyIncomes(
        oldAmount: BigDecimal,
        oldAmountPaid: BigDecimal,
        newAmount: BigDecimal,
        newAmountPaid: BigDecimal
    ): OrganizationEntity {
        val amount = oldAmount.negate() + newAmount
        val amountPaid = oldAmountPaid.negate() + newAmountPaid

        organizationBalanceMutateService.modifyAvailableBalance(oldAmountPaid, newAmountPaid)
        return increaseIncomes(amount = amount, amountPaid = amountPaid)
    }
}

