package com.project.management.money.organization

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.organization.models.OrganizationEntity
import com.project.management.organization.repositories.OrganizationRepository
import com.project.management.organization.validators.OrganizationValidator
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal

@Service
class OrganizationBalanceMutateService(
    private val organizationRepository: OrganizationRepository,
    private val currentUser: CurrentUserConfig,
    private val organizationValidator: OrganizationValidator,
) {

    @Transactional
    fun increaseAvailableBalance(amount: BigDecimal): OrganizationEntity {
        val user = currentUser.getCurrentUser()
        val organization = organizationValidator.validateOrganizationExistsByIdAndOrganizationId(
            user.organizationId
        )
        
        val updatedOrganization = organization.copy(
            availableBalance = organization.availableBalance.add(amount),
            updatedBy = user.id
        )
        
        return organizationRepository.save(updatedOrganization)
    }

    @Transactional
    fun decreaseAvailableBalance(amount: BigDecimal): OrganizationEntity {
        val user = currentUser.getCurrentUser()
        val organization = organizationValidator.validateOrganizationExistsByIdAndOrganizationId(
            user.organizationId
        )
        
        val updatedOrganization = organization.copy(
            availableBalance = organization.availableBalance.minus(amount),
            updatedBy = user.id
        )
        
        return organizationRepository.save(updatedOrganization)
    }

    @Transactional
    fun modifyAvailableBalance(oldAmount: BigDecimal, newAmount: BigDecimal): OrganizationEntity {
        val user = currentUser.getCurrentUser()
        val organization = organizationValidator.validateOrganizationExistsByIdAndOrganizationId(
            user.organizationId
        )
        
        val updatedOrganization = organization.copy(
            availableBalance = organization.availableBalance.minus(oldAmount).add(newAmount),
            updatedBy = user.id
        )
        
        return organizationRepository.save(updatedOrganization)
    }
}
