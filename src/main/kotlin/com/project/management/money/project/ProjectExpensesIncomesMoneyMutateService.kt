package com.project.management.money.project

import com.project.management.beneficiaries.mappers.toEntity
import com.project.management.beneficiaries.models.BeneficiaryTransaction
import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.projects.models.ProjectEntity
import com.project.management.projects.models.ProjectExpense
import com.project.management.projects.models.ProjectExpenseEntity
import com.project.management.projects.repositories.ProjectExpenseMutateRepository
import com.project.management.projects.repositories.ProjectRepository
import com.project.management.projects.requests.PostRequestProjectExpense
import com.project.management.projects.validators.ProjectValidator
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal

@Service
class ProjectExpensesIncomesMoneyMutateService(
    private val transactions: ProjectExpenseMutateRepository,
    private val projectRepository: ProjectRepository,
    private val currentUser: CurrentUserConfig,
    private val projectValidator: ProjectValidator,
) {

    @Transactional
    fun projectExpenseAdd(
        request: PostRequestProjectExpense,
        transaction: BeneficiaryTransaction,
        projectId: Long
    ): ProjectExpenseEntity {
        increaseExpenses(
            projectId = projectId,
            amount = request.amount.toBigDecimal(),
            amountPaid = request.amountPaid.toBigDecimal()
        )

        return createExpense(request, transaction, projectId)
    }

    @Transactional
    fun projectExpenseDelete(expense: ProjectExpense): ProjectEntity {
        deleteExpense(projectExpenseId = expense.id!!)
        return decreaseExpenses(
            projectId = expense.projectId,
            amount = expense.beneficiaryTransaction.amount,
            amountPaid = expense.beneficiaryTransaction.amountPaid
        )
    }

    @Transactional
    fun projectExpenseModify(
        projectId: Long,
        oldAmount: BigDecimal,
        oldAmountPaid: BigDecimal,
        newAmount: BigDecimal,
        newAmountPaid: BigDecimal
    ): ProjectEntity {
        val amount = oldAmount.negate() + newAmount
        val amountPaid = oldAmountPaid.negate() + newAmountPaid

        return increaseExpenses(
            projectId = projectId,
            amount = amount,
            amountPaid = amountPaid,
        )
    }

    private fun increaseExpenses(
        projectId: Long, amount: BigDecimal, amountPaid: BigDecimal
    ): ProjectEntity {
        val currentUser = currentUser.getCurrentUser()
        var project = projectValidator.validateExistsByIdAndOrganizationId(
            projectId = projectId,
            organizationId = currentUser.organizationId
        )
        val editedProject = project.copy(
            totalExpenses = project.totalExpenses.plus(amount),
            totalPaidExpenses = project.totalPaidExpenses.plus(amountPaid),
            updatedBy = currentUser.id
        )

        project = projectRepository.save(editedProject)

        return project
    }

    private fun decreaseExpenses(
        projectId: Long, amount: BigDecimal, amountPaid: BigDecimal
    ): ProjectEntity {
        val currentUser = currentUser.getCurrentUser()
        var project = projectValidator.validateExistsByIdAndOrganizationId(
            projectId = projectId,
            organizationId = currentUser.organizationId
        )
        val editedProject = project.copy(
            totalExpenses = project.totalExpenses.minus(amount),
            totalPaidExpenses = project.totalPaidExpenses.minus(amountPaid),
            updatedBy = currentUser.id
        )

        project = projectRepository.save(editedProject)

        return project
    }

    private fun createExpense(
        request: PostRequestProjectExpense,
        transaction: BeneficiaryTransaction,
        projectId: Long
    ): ProjectExpenseEntity {
        val currentUser = currentUser.getCurrentUser()
        val entity = request
            .toEntity(
                projectId,
                beneficiaryTransaction = transaction,
                user = currentUser,
                version = 1
            )

        return transactions.save(entity)
    }

    private fun deleteExpense(
        projectExpenseId: Long
    ) {
        transactions.deleteByIdAndOrganizationId(
            id = projectExpenseId,
            organizationId = currentUser.getCurrentUser().organizationId,
            updatedBy = currentUser.getCurrentUser().id!!
        )
    }
}
