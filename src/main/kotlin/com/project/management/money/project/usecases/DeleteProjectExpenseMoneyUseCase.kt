package com.project.management.money.project.usecases

import com.project.management.beneficiaries.services.BeneficiaryTransactionMutateService
import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.money.MoneyValidationService
import com.project.management.money.user.UserMoneyMutateService
import com.project.management.money.beneficiary.BeneficiaryMoneyMutateService
import com.project.management.money.organization.OrganizationProjectsMoneyMutateService
import com.project.management.money.project.ProjectExpensesIncomesMoneyMutateService
import com.project.management.projects.models.ProjectExpense
import com.project.management.projects.repositories.ProjectExpenseMutateRepository
import com.project.management.projects.validators.ProjectExpenseValidator
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class DeleteProjectExpenseMoneyUseCase(
    private val currentUser: CurrentUserConfig,
    private val projectExpenseValidator: ProjectExpenseValidator,
    private val beneficiary: BeneficiaryMoneyMutateService,
    private val projectMoneyMutateService: ProjectExpensesIncomesMoneyMutateService,
    private val userMoneyMutateService: UserMoneyMutateService,
    private val organizationProjectsMoneyMutateService: OrganizationProjectsMoneyMutateService,
    private val beneficiaryTransactionMutateService: BeneficiaryTransactionMutateService,
    private val projectExpenseRepository: ProjectExpenseMutateRepository,
    private val money: MoneyValidationService
) {

    @Transactional
    fun delete(projectExpenseId: Long) {
        val user = currentUser.getCurrentUser()

        // Get existing expense for deletion
        val existingExpense = projectExpenseValidator.validateExistsByIdAndOrganization(
            projectExpenseId = projectExpenseId,
            organizationId = user.organizationId
        )

        userScenario(existingExpense)
        projectScenarios(existingExpense)
        organizationScenario(existingExpense)

        // Delete beneficiary transaction
        transactionScenario(existingExpense)
    }

    private fun userScenario(expense: ProjectExpense) {
        userMoneyMutateService.projectExpenseDelete(
            userId = expense.createdBy!!,
            amount = expense.beneficiaryTransaction.amount.toDouble(),
            description = expense.beneficiaryTransaction.description,
            project = expense.project
        )
    }

    private fun transactionScenario(expense: ProjectExpense) {
        beneficiary.projectExpenseDelete(
            beneficiaryId = expense.beneficiaryId,
            transactionId = expense.beneficiaryTransaction.id!!,
            amount = expense.beneficiaryTransaction.amount,
            amountPaid = expense.beneficiaryTransaction.amountPaid
        )
    }

    private fun organizationScenario(expense: ProjectExpense) {
        organizationProjectsMoneyMutateService.decreaseExpenses(
            amount = expense.beneficiaryTransaction.amount,
            amountPaid = expense.beneficiaryTransaction.amountPaid
        )
    }

    private fun projectScenarios(expense: ProjectExpense) {
        projectMoneyMutateService.projectExpenseDelete(expense = expense)
    }
}
