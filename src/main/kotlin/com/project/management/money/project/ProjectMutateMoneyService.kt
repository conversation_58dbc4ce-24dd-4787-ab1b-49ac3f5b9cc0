package com.project.management.money.project

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.projects.models.ProjectEntity
import com.project.management.projects.repositories.ProjectRepository
import com.project.management.projects.validators.ProjectValidator
import org.springframework.stereotype.Service
import java.math.BigDecimal
import jakarta.transaction.Transactional

@Service
class ProjectMutateMoneyService(
    private val projectRepository: ProjectRepository,
    private val currentUser: CurrentUserConfig,
    private val projectValidator: ProjectValidator,
) {
    @Transactional
    fun modifyExpenses(projectId: Long, amount: BigDecimal, paid: BigDecimal): ProjectEntity {
        val user = currentUser.getCurrentUser()
        val project = projectValidator.validateExistsByIdAndOrganizationId(
            projectId = projectId,
            organizationId = user.organizationId
        )
        val updatedProject = project.copy(
            totalExpenses = project.totalExpenses.add(amount),
            totalPaidExpenses = project.totalPaidExpenses.add(paid),
            updatedBy = user.id
        )

        return projectRepository.save(updatedProject)
    }

    @Transactional
    fun modifyIncomes(projectId: Long, amount: BigDecimal, paid: BigDecimal): ProjectEntity {
        val user = currentUser.getCurrentUser()
        val project = projectValidator.validateExistsByIdAndOrganizationId(
            projectId = projectId,
            organizationId = user.organizationId
        )
        val updatedProject = project.copy(
            totalIncomes = project.totalIncomes.add(amount),
            totalPaidIncomes = project.totalPaidIncomes.add(paid),
            updatedBy = user.id
        )

        return projectRepository.save(updatedProject)
    }
}
