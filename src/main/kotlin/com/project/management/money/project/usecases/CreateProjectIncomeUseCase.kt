package com.project.management.money.project.usecases

import com.project.management.customers.models.CustomerTransaction
import com.project.management.customers.validators.CustomerValidator
import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.exceptions.BusinessException
import com.project.management.money.user.UserMoneyMutateService
import com.project.management.money.customer.CustomerMoneyMutateService
import com.project.management.money.organization.OrganizationProjectsMoneyMutateService
import com.project.management.money.project.ProjectExpensesIncomesMoneyMutateService
import com.project.management.projects.models.ProjectIncomeEntity
import com.project.management.projects.requests.PostRequestProjectIncome
import com.project.management.projects.validators.ProjectIncomeValidator
import com.project.management.projects.validators.ProjectValidator
import com.project.management.users.models.User
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class CreateProjectIncomeUseCase(
    private val currentUser: CurrentUserConfig,
    private val projectValidator: ProjectValidator,
    private val customerValidator: CustomerValidator,
    private val projectIncomeValidator: ProjectIncomeValidator,
    private val customer: CustomerMoneyMutateService,
    private val projectMoneyMutateService: ProjectExpensesIncomesMoneyMutateService,
    private val userMoneyMutateService: UserMoneyMutateService,
    private val organizationProjectsMoneyMutateService: OrganizationProjectsMoneyMutateService
) {

    @Transactional
    fun create(request: PostRequestProjectIncome, projectId: Long): ProjectIncomeEntity {
        val user = currentUser.getCurrentUser()
        validate(request, user, projectId)

        val transaction = customerScenario(request, projectId)
        val entity = projectScenarios(request, projectId, transaction)
        val income = projectIncomeValidator.validateExistsByIdAndOrganization(
            projectIncomeId = entity.id!!,
            organizationId = user.organizationId
        )

        organizationScenario(income = income)

        return income
    }

    private fun customerScenario(
        request: PostRequestProjectIncome,
        projectId: Long
    ): CustomerTransaction {
        return customer.projectIncomeAdd(request = request, projectId = projectId)
    }

    private fun organizationScenario(income: ProjectIncomeEntity) {
        organizationProjectsMoneyMutateService.increaseIncomes(
            amount = income.customerTransaction.amount,
            amountPaid = income.customerTransaction.amountPaid
        )
    }

    private fun projectScenarios(
        request: PostRequestProjectIncome,
        projectId: Long,
        transaction: CustomerTransaction
    ): ProjectIncomeEntity {
        return projectMoneyMutateService.projectIncomeAdd(
            request = request,
            projectId = projectId,
            transaction = transaction
        )
    }

    private fun validate(request: PostRequestProjectIncome, user: User, projectId: Long) {
        // Validate amount constraints
        if (request.amountPaid > request.amount) {
            throw BusinessException.BadRequestException(
                message = "Amount paid cannot be greater than amount."
            )
        }

        // Validate project exists
        projectValidator.validateExistsByIdAndOrganizationId(
            projectId = projectId,
            organizationId = user.organizationId
        )

        // Validate customer exists
        customerValidator.validateCustomerExistsByIdAndOrganizationId(
            customerId = request.customerId,
            organizationId = user.organizationId
        )
    }
}
