package com.project.management.money.project

import com.project.management.money.project.usecases.CreateProjectExpenseUseCase
import com.project.management.money.project.usecases.CreateProjectIncomeUseCase
import com.project.management.money.project.usecases.DeleteProjectExpenseMoneyUseCase
import com.project.management.money.project.usecases.UpdateProjectExpenseMoneyUseCase
import com.project.management.projects.models.ProjectExpense
import com.project.management.projects.models.ProjectIncomeEntity
import com.project.management.projects.requests.PatchRequestProjectExpenseAmount
import com.project.management.projects.requests.PostRequestProjectExpense
import com.project.management.projects.requests.PostRequestProjectIncome
import jakarta.transaction.Transactional
import org.springframework.stereotype.Service

@Service
class ProjectMoneyMutateService(
    private val createExpense: CreateProjectExpenseUseCase,
    private val updateExpense: UpdateProjectExpenseMoneyUseCase,
    private val deleteExpense: DeleteProjectExpenseMoneyUseCase,
    private val createIncome: CreateProjectIncomeUseCase
) {

    @Transactional
    fun projectExpenseAdd(request: PostRequestProjectExpense, projectId: Long): ProjectExpense {
        return createExpense.create(request, projectId)
    }

    @Transactional
    fun projectExpenseUpdate(request: PatchRequestProjectExpenseAmount, projectExpenseId: Long): ProjectExpense {
        return updateExpense.update(request, projectExpenseId)
    }

    @Transactional
    fun projectExpenseDelete(projectExpenseId: Long) {
        deleteExpense.delete(projectExpenseId)
    }

    @Transactional
    fun projectIncomeAdd(request: PostRequestProjectIncome, projectId: Long): ProjectIncomeEntity {
        return createIncome.create(request, projectId)
    }
}