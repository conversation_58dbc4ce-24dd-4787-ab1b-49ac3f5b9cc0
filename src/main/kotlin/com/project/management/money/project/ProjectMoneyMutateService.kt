package com.project.management.money.project

import com.project.management.beneficiaries.models.BeneficiaryTransactionEntity
import com.project.management.projects.models.ProjectExpense
import com.project.management.projects.models.ProjectExpenseEntity
import com.project.management.projects.requests.PatchRequestProjectExpenseAmount
import com.project.management.projects.requests.PostRequestProjectExpense
import jakarta.transaction.Transactional
import org.springframework.stereotype.Service

@Service
class ProjectMoneyMutateService(
    private val createExpense: CreateProjectExpenseUseCase,
    private val updateExpense: UpdateProjectExpenseMoneyUseCase
) {

    @Transactional
    fun projectExpenseAdd(request: PostRequestProjectExpense, projectId: Long): ProjectExpense {
        return createExpense.create(request, projectId)
    }

    @Transactional
    fun projectExpenseUpdate(request: PatchRequestProjectExpenseAmount, projectExpenseId: Long): ProjectExpense {
        return updateExpense.update(request, projectExpenseId)
    }
}