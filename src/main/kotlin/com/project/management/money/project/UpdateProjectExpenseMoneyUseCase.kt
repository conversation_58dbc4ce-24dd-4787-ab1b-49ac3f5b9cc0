package com.project.management.money.project

import com.project.management.beneficiaries.services.BeneficiaryTransactionMutateService
import com.project.management.beneficiaries.validators.BeneficiaryValidator
import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.exceptions.BusinessException
import com.project.management.money.MoneyValidationService
import com.project.management.money.user.UserMoneyMutateService
import com.project.management.money.organization.OrganizationProjectsMoneyMutateService
import com.project.management.projects.models.ProjectExpense
import com.project.management.projects.models.ProjectExpenseEntity
import com.project.management.projects.mappers.toEntity
import com.project.management.projects.repositories.ProjectExpenseMutateRepository
import com.project.management.projects.requests.PutRequestProjectExpense
import com.project.management.projects.requests.PatchRequestProjectExpenseAmount
import com.project.management.projects.validators.ProjectExpenseValidator
import com.project.management.projects.validators.ProjectValidator
import com.project.management.terms.validators.TermValidator
import com.project.management.terms.validators.TermsGroupValidator
import com.project.management.users.models.User
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal

@Component
class UpdateProjectExpenseMoneyUseCase(
    private val currentUser: CurrentUserConfig,
    private val projectValidator: ProjectValidator,
    private val termsGroupValidator: TermsGroupValidator,
    private val termValidator: TermValidator,
    private val beneficiaryValidator: BeneficiaryValidator,
    private val projectExpenseValidator: ProjectExpenseValidator,
    private val beneficiaryTransactionMutateService: BeneficiaryTransactionMutateService,
    private val projectMutateMoneyService: ProjectMutateMoneyService,
    private val userMoneyMutateService: UserMoneyMutateService,
    private val organizationProjectsMoneyMutateService: OrganizationProjectsMoneyMutateService,
    private val projectExpenseRepository: ProjectExpenseMutateRepository,
    private val money: MoneyValidationService
) {

    @Transactional
    fun update(request: PutRequestProjectExpense, projectExpenseId: Long): ProjectExpense {
        val user = currentUser.getCurrentUser()

        // Get existing expense for comparison
        val existingExpense = projectExpenseValidator.validateExistsByIdAndOrganization(
            projectExpenseId = projectExpenseId,
            organizationId = user.organizationId
        )

        validate(request, user, existingExpense)

        // Update user balance
        userMoneyMutateService.projectExpenseModify(
            userId = existingExpense.createdBy!!,
            newAmount = request.amount.toBigDecimal(),
            expense = existingExpense
        )

        // Update project totals
        projectMutateMoneyService.modifyExpenses(
            projectId = existingExpense.projectId,
            amount = existingExpense.beneficiaryTransaction.amount.negate() + request.amount.toBigDecimal(),
            paid = existingExpense.beneficiaryTransaction.amountPaid.negate() + request.amountPaid.toBigDecimal()
        )

        // Update organization totals
        organizationProjectsMoneyMutateService.modifyExpenses(
            oldAmount = existingExpense.beneficiaryTransaction.amount,
            oldAmountPaid = existingExpense.beneficiaryTransaction.amountPaid,
            newAmount = request.amount.toBigDecimal(),
            newAmountPaid = request.amountPaid.toBigDecimal()
        )

        // Update beneficiary transaction amount
        val amountRequest = PatchRequestProjectExpenseAmount(
            amount = request.amount,
            amountPaid = request.amountPaid,
            transactionVersion = request.transactionVersion
        )
        beneficiaryTransactionMutateService.updateAmount(
            request = amountRequest,
            beneficiaryTransactionId = existingExpense.beneficiaryTransaction.id!!
        )

        // Update project expense entity (terms and beneficiary)
        updateProjectExpenseEntity(request, existingExpense, user)

        money.validate(organizationId = user.organizationId)

        return projectExpenseValidator.validateExistsByIdAndOrganization(
            projectExpenseId = projectExpenseId,
            organizationId = user.organizationId
        )
    }

    private fun updateProjectExpenseEntity(
        request: PutRequestProjectExpense,
        existingExpense: ProjectExpense,
        user: User
    ) {
        val expenseEntity = existingExpense.toEntity()

        val updatedExpense = expenseEntity.copy(
            beneficiaryId = request.beneficiaryId,
            termsGroupId = request.termsGroupId,
            termId = request.termId,
            updatedBy = user.id!!
        )

        projectExpenseRepository.save(updatedExpense)
    }

    private fun validate(request: PutRequestProjectExpense, user: User, existingExpense: ProjectExpense) {
        // Check admin permission
        if (!user.isAdmin) {
            throw BusinessException.ForbiddenException(message = "You do not have permission to update this transaction.")
        }

        // Check version conflicts
        if (existingExpense.version != request.version) {
            throw BusinessException.ConflictException(message = "Project expense version conflict.")
        }

        if (existingExpense.beneficiaryTransaction.version != request.transactionVersion) {
            throw BusinessException.ConflictException(message = "Transaction version conflict.")
        }

        // Validate amount constraints
        if (request.amountPaid > request.amount) {
            throw BusinessException.BadRequestException(
                message = "Amount paid cannot be greater than amount."
            )
        }

        // Validate terms group and term
        val termsGroup = termsGroupValidator.validateTermsGroupExistsByIdAndOrganizationId(
            termsGroupId = request.termsGroupId,
            organizationId = user.organizationId
        )
        val term = termValidator.validateTermExistsByIdAndOrganizationId(
            termId = request.termId,
            organizationId = user.organizationId
        )

        if (term.termsGroupId != termsGroup.id) {
            throw BusinessException.ConflictException(message = "Term and TermsGroup do not match.")
        }

        // Validate project exists
        projectValidator.validateExistsByIdAndOrganizationId(
            projectId = existingExpense.projectId,
            organizationId = user.organizationId
        )

        // Validate beneficiary exists
        beneficiaryValidator.validateBeneficiaryExistsByIdAndOrganizationId(
            beneficiaryId = request.beneficiaryId,
            organizationId = user.organizationId
        )
    }
}
