package com.project.management.money.project

import com.project.management.beneficiaries.validators.BeneficiaryValidator
import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.exceptions.BusinessException
import com.project.management.money.MoneyValidationService
import com.project.management.money.user.UserMoneyMutateService

import com.project.management.money.beneficiary.BeneficiaryMoneyMutateService
import com.project.management.money.organization.OrganizationProjectsMoneyMutateService
import com.project.management.projects.models.ProjectExpense
import com.project.management.projects.requests.PatchRequestProjectExpenseAmount
import com.project.management.projects.validators.ProjectExpenseValidator
import com.project.management.projects.validators.ProjectValidator
import com.project.management.terms.validators.TermValidator
import com.project.management.terms.validators.TermsGroupValidator
import com.project.management.users.models.User
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal

@Component
class UpdateProjectExpenseMoneyUseCase(
    private val currentUser: CurrentUserConfig,
    private val projectValidator: ProjectValidator,
    private val beneficiaryValidator: BeneficiaryValidator,
    private val projectExpenseValidator: ProjectExpenseValidator,
    private val beneficiary: BeneficiaryMoneyMutateService,
    private val projectMoneyMutateService: ProjectExpensesIncomesMoneyMutateService,
    private val userMoneyMutateService: UserMoneyMutateService,
    private val organizationProjectsMoneyMutateService: OrganizationProjectsMoneyMutateService,
    private val money: MoneyValidationService
) {

    @Transactional
    fun update(request: PatchRequestProjectExpenseAmount, projectExpenseId: Long): ProjectExpense {
        val user = currentUser.getCurrentUser()

        // Get existing expense for comparison
        val existingExpense = projectExpenseValidator.validateExistsByIdAndOrganization(
            projectExpenseId = projectExpenseId,
            organizationId = user.organizationId
        )

        validate(request, user, existingExpense)

        beneficiaryScenario(request, existingExpense)
        projectScenarios(request, existingExpense)
        val updatedExpense = projectExpenseValidator.validateExistsByIdAndOrganization(
            projectExpenseId = projectExpenseId,
            organizationId = user.organizationId
        )

        userScenario(oldExpense = existingExpense, newExpense = updatedExpense)
        organizationScenario(oldExpense = existingExpense, newExpense = updatedExpense)

        money.validate(organizationId = user.organizationId)
        return updatedExpense
    }

    private fun userScenario(oldExpense: ProjectExpense, newExpense: ProjectExpense) {
        userMoneyMutateService.projectExpenseModify(
            userId = oldExpense.createdBy!!,
            newAmount = newExpense.beneficiaryTransaction.amount,
            expense = oldExpense
        )
    }

    private fun beneficiaryScenario(
        request: PatchRequestProjectExpenseAmount,
        existingExpense: ProjectExpense
    ) {
        beneficiary.projectExpenseModify(
            beneficiaryId = existingExpense.beneficiaryId,
            oldAmount = existingExpense.beneficiaryTransaction.amount,
            oldAmountPaid = existingExpense.beneficiaryTransaction.amountPaid,
            newAmount = request.amount.toBigDecimal(),
            newAmountPaid = request.amountPaid.toBigDecimal()
        )
    }

    private fun organizationScenario(oldExpense: ProjectExpense, newExpense: ProjectExpense) {
        organizationProjectsMoneyMutateService.modifyExpenses(
            oldAmount = oldExpense.beneficiaryTransaction.amount,
            oldAmountPaid = oldExpense.beneficiaryTransaction.amountPaid,
            newAmount = newExpense.beneficiaryTransaction.amount,
            newAmountPaid = newExpense.beneficiaryTransaction.amountPaid
        )
    }

    private fun projectScenarios(
        request: PatchRequestProjectExpenseAmount,
        existingExpense: ProjectExpense
    ) {
        projectMoneyMutateService.projectExpenseModify(
            projectId = existingExpense.projectId,
            oldAmount = existingExpense.beneficiaryTransaction.amount,
            oldAmountPaid = existingExpense.beneficiaryTransaction.amountPaid,
            newAmount = request.amount.toBigDecimal(),
            newAmountPaid = request.amountPaid.toBigDecimal()
        )
    }

    private fun validate(request: PatchRequestProjectExpenseAmount, user: User, existingExpense: ProjectExpense) {
        // Check admin permission
        if (!user.isAdmin) {
            throw BusinessException.ForbiddenException(message = "You do not have permission to update this transaction.")
        }

        // Check version conflicts
        if (existingExpense.version != request.transactionVersion) {
            throw BusinessException.ConflictException(message = "Project expense version conflict.")
        }

        if (existingExpense.beneficiaryTransaction.version != request.transactionVersion) {
            throw BusinessException.ConflictException(message = "Transaction version conflict.")
        }

        // Validate amount constraints
        if (request.amountPaid > request.amount) {
            throw BusinessException.BadRequestException(
                message = "Amount paid cannot be greater than amount."
            )
        }

        // Validate project exists
        projectValidator.validateExistsByIdAndOrganizationId(
            projectId = existingExpense.projectId,
            organizationId = user.organizationId
        )

        // Validate beneficiary exists
        beneficiaryValidator.validateBeneficiaryExistsByIdAndOrganizationId(
            beneficiaryId = existingExpense.beneficiaryId,
            organizationId = user.organizationId
        )
    }
}
