package com.project.management.users.services

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.exceptions.BusinessException
import com.project.management.money.user.UserMoneyMutateService
import com.project.management.users.requests.UserRequestDto
import com.project.management.users.responses.UserResponseDto
import com.project.management.users.models.User
import com.project.management.projects.models.UserProjectAccessEntity
import com.project.management.users.mappers.UserMapper
import com.project.management.projects.repositories.ProjectAccessRepository
import com.project.management.users.repositories.UserRepository
import com.project.management.auth.validators.AuthValidator
import com.project.management.common.utility.Constants
import com.project.management.organization.validators.OrganizationValidator
import com.project.management.users.mappers.toEntity
import com.project.management.users.validators.UserValidator
import jakarta.persistence.EntityManager
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.stereotype.Service

@Service
class UsersService(
    private val userRepository: UserRepository,
    private val projectAccessRepository: ProjectAccessRepository,
    private val currentUser: CurrentUserConfig,
    private val userMapper: UserMapper,
    private val userValidator: UserValidator,
    private val authValidator: AuthValidator,
    private val passwordEncoder: PasswordEncoder,
    private val organization: OrganizationValidator,
    private val userMoneyMutateService: UserMoneyMutateService
) {

    @Autowired
    private lateinit var entityManager: EntityManager

    fun getAll(): List<User> {
        val user = currentUser.getCurrentUser()
        val session = entityManager.unwrap(org.hibernate.Session::class.java)
        session.enableFilter(Constants.softDeleteFilter)
        val users = userRepository.findAllByOrganizationId(user.organizationId)
        session.disableFilter(Constants.softDeleteFilter)
        return users
    }

    fun getUser(userId: Long): UserResponseDto {
        val user = currentUser.getCurrentUser()
        val result = userValidator.validateUserExistsByIdAndOrganizationId(
            userId = userId,
            organizationId = user.organizationId
        )
        return userMapper.toUserResponseDto(result)
    }

    fun getAllProjectAccess(projectId: Long): List<UserProjectAccessEntity> {
        val user = currentUser.getCurrentUser()
        val projectAccess = projectAccessRepository.findAllByOrganizationIdAndProjectId(
            organizationId = user.organizationId,
            projectId = projectId
        )
        return userRepository.findAllById(projectAccess.map { it.userId }).map { user ->
            UserProjectAccessEntity(
                user = user,
                projectAccess = projectAccess.find { it.userId == user.id }!!
            )
        }
    }

    fun getProjectAccessByUserIdAndProjectId(userId: Long, projectId: Long): UserProjectAccessEntity {
        val current = currentUser.getCurrentUser()
        val user = userValidator.validateUserExistsByIdAndOrganizationId(
            userId = userId,
            organizationId = current.organizationId
        )
        val projectAccess = projectAccessRepository.findByUserIdAndProjectId(user.id!!, projectId)
            ?: throw BusinessException.NotFoundException(message = "Project access does not exist.")
        return UserProjectAccessEntity(
            user = user,
            projectAccess = projectAccess
        )
    }

    fun create(userDto: UserRequestDto): User {
        val currentUser = currentUser.getCurrentUser()
        authValidator.isPasswordMatch(userDto.password, userDto.confirmPassword)
        val validation = runCatching {
            userValidator.validateUserExistsByUsernameAndOrganizationCode(
                username = userDto.username,
                organizationCode = currentUser.organizationCode
            )
        }
        if (validation.isSuccess) {
            throw BusinessException.ConflictException(message = "User with username ${userDto.username} already exists.")
        }
        val user = userDto.toEntity(
            organizationId = currentUser.organizationId,
            createdBy = currentUser.id,
            updatedBy = currentUser.id,
            organizationCode = currentUser.organizationCode,
            isAdmin = userDto.isAdmin
        )
        user.password = passwordEncoder.encode(user.password)
        user.organizationId = currentUser.organizationId
        user.organizationCode = currentUser.organizationCode
        user.createdBy = currentUser.id
        user.updatedBy = currentUser.id
        userRepository.save(user)
        return user
    }

    @Transactional
    fun delete(userId: Long) {
        val currentUser = currentUser.getCurrentUser()
        val user = userValidator.validateUserExistsByIdAndOrganizationId(
            userId = userId,
            organizationId = currentUser.organizationId
        )
        val organization = organization.validateOrganizationExistsById(
            organizationId = user.organizationId
        )
        if (!currentUser.isAdmin) throw BusinessException.ForbiddenException(message = "You do not have permission to delete this user.")
        if (user.balance.toDouble() != 0.0) throw BusinessException.BadRequestException(message = "Cannot delete user with balance not equal to zero (${user.balance}).")
        if (user.id == organization.ownerId) throw BusinessException.BadRequestException(message = "Cannot delete organization owner.")

        userRepository.deleteByIdAndOrganizationId(
            id = user.id!!,
            organizationId = user.organizationId,
            updatedBy = currentUser.id!!
        )
    }
}