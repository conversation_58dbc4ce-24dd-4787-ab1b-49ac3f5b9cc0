package com.project.management.users.models

enum class UserTransactionTag {
    PROJECT_EXPENSE_ADD,
    PROJECT_EXPENSE_MODIFY,
    PROJECT_EXPENSE_DELETE,

    PROJECT_INCOME_ADD,
    PROJECT_INCOME_MODIFY,
    PROJECT_INCOME_DELETE,

    BALANCE_ADD,
    BALANCE_WITHDRAW,
    BALANCE_MODIFY,
    BALANCE_SETTLEMENT;

    fun isProjectExpense(): Boolean {
        return this == PROJECT_EXPENSE_ADD || this == PROJECT_EXPENSE_MODIFY || this == PROJECT_EXPENSE_DELETE
    }
}
