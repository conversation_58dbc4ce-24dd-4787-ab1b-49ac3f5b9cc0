package com.project.management.beneficiaries.repositories

import com.project.management.beneficiaries.models.BeneficiaryTransaction
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface BeneficiaryTransactionQueryRepository : JpaRepository<BeneficiaryTransaction, Long> {

    fun findAllByOrganizationId(organizationId: Long): List<BeneficiaryTransaction>

    fun findAllByOrganizationIdAndBeneficiaryId(organizationId: Long, beneficiaryId: Long): List<BeneficiaryTransaction>

    fun findByIdAndOrganizationId(id: Long, organizationId: Long): BeneficiaryTransaction?
}

@Repository
interface BeneficiaryTransactionMutateRepository : JpaRepository<BeneficiaryTransaction, Long> {
    @Modifying
    @Query(
        value = "UPDATE beneficiary_transactions SET deleted = NOW(), updated_by = :updatedBy WHERE id = :id AND organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun deleteByIdAndOrganizationId(id: Long, organizationId: Long, updatedBy: Long)
}