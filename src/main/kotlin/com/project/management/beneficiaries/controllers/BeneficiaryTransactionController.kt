package com.project.management.beneficiaries.controllers

import com.project.management.beneficiaries.models.BeneficiaryTransaction
import com.project.management.beneficiaries.requests.PatchRequestBeneficiaryTransaction
import com.project.management.beneficiaries.services.BeneficiaryTransactionMutateService
import com.project.management.beneficiaries.services.BeneficiaryTransactionQueryService
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/v1/beneficiaries/{beneficiaryId}/transactions")
class BeneficiaryTransactionController(
    private val query: BeneficiaryTransactionQueryService,
    private val mutate: BeneficiaryTransactionMutateService,
) {
    @GetMapping
    fun getAll(
        @PathVariable beneficiaryId: Long
    ): ResponseEntity<List<BeneficiaryTransaction>> {
        return ResponseEntity.ok(query.getAllByBeneficiaryId(beneficiaryId))
    }

    @PatchMapping("/{beneficiaryTransactionId}")
    fun modifyTransaction(
        @PathVariable beneficiaryTransactionId: Long,
        @RequestBody request: PatchRequestBeneficiaryTransaction
    ): ResponseEntity<BeneficiaryTransaction> {
        return ResponseEntity.ok(mutate.updateTransaction(request, beneficiaryTransactionId))
    }
}